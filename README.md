# Excel Field Checker

Ứng dụng Node.js được viết theo phong cách lập trình hướng đối tượng để kiểm tra các trường `[@field]` trong file Excel dựa trên cấu hình sections.

## 🎯 Tính năng

- ✅ Kiểm tra các trường có pattern `[@field_name]` trong file Excel
- 📋 Cấu hình sections linh hoạt (header, detail, footer, etc.)
- 📊 Báo cáo chi tiết với thống kê theo section
- 🔧 Tạo file Excel mẫu để test
- 💾 Xuất báo cáo ra file JSON
- 🧪 Test suite đầy đủ

## 📦 Cài đặt

```bash
# Clone hoặc tải về project
cd excel-field-checker

# Cài đặt dependencies
npm install
```

## 🚀 Sử dụng

### 1. Tạo file Excel mẫu

```bash
npm start generate
# hoặc với tên file tùy chỉnh
npm start generate my_sample.xlsx
```

### 2. <PERSON><PERSON><PERSON> tra file Excel

```bash
npm start check path/to/your/file.xlsx
```

### 3. Chạy test

```bash
npm test
```

### 4. Xem hướng dẫn

```bash
npm start help
```

## 📐 Cấu trúc file Excel

### Sheet 1: Dữ liệu
Chứa dữ liệu với các trường `[@field_name]` cần kiểm tra.

Ví dụ:
```
A1: Company: [@company_name]
A2: Report Date: [@report_date]
A3: Customer: [@customer_name]
B1: Address: [@company_address]
B2: Amount: [@amount]
```

### Sheet 2: Cấu hình
Định nghĩa các sections và phạm vi rows.

Format:
| section | start_row | end_row |
|---------|-----------|---------|
| header  | 1         | 10      |
| detail  | 11        | 11      |
| footer  | 12        | 12      |

## 🏗️ Kiến trúc

### Classes chính:

#### `ExcelFieldChecker`
- **Mục đích**: Class chính để kiểm tra các trường [@field]
- **Phương thức chính**:
  - `initialize()`: Đọc file Excel
  - `loadConfiguration()`: Đọc cấu hình từ sheet 2
  - `findFields()`: Tìm kiếm các trường [@field]
  - `generateReport()`: Tạo báo cáo
  - `run()`: Thực hiện toàn bộ quy trình

#### `ExcelGenerator`
- **Mục đích**: Tạo file Excel mẫu để test
- **Phương thức chính**:
  - `createDataSheet()`: Tạo sheet dữ liệu
  - `createConfigSheet()`: Tạo sheet cấu hình
  - `saveFile()`: Lưu file Excel
  - `createCustomFile()`: Tạo file với dữ liệu tùy chỉnh

#### `Application`
- **Mục đích**: Điều khiển ứng dụng và xử lý command line
- **Phương thức chính**:
  - `checkFile()`: Xử lý lệnh check
  - `generateSample()`: Xử lý lệnh generate
  - `showHelp()`: Hiển thị hướng dẫn

#### `TestRunner`
- **Mục đích**: Chạy test suite
- **Phương thức chính**:
  - `testGenerateExcel()`: Test tạo file Excel
  - `testCheckExcel()`: Test kiểm tra file
  - `testEdgeCases()`: Test các trường hợp đặc biệt
  - `testPerformance()`: Test hiệu suất

## 📊 Kết quả

### Console Output
```
🚀 Bắt đầu kiểm tra trường [@field] trong Excel...

✅ Đã đọc file Excel: examples/sample_excel.xlsx
📋 Đang đọc cấu hình từ sheet 2...
   📌 header: rows 1-10
   📌 detail: rows 11-11
   📌 footer: rows 12-12
🔍 Đang tìm kiếm các trường [@field]...
   🎯 Tìm thấy: [@company_name] tại A2 (header)
   🎯 Tìm thấy: [@report_date] tại A3 (header)
   ...
✅ Tổng cộng tìm thấy 15 trường [@field]

============================================================
📊 BÁO CÁO KIỂM TRA TRƯỜNG [@FIELD]
============================================================
📁 File: examples/sample_excel.xlsx
⏰ Thời gian: 23/07/2025 10:30:45
📋 Tổng số trường tìm thấy: 15

📐 CẤU HÌNH SECTIONS:
   header: rows 1-10
   detail: rows 11-11
   footer: rows 12-12

📈 THỐNG KÊ THEO SECTION:
   header: 10 trường
   detail: 3 trường
   footer: 2 trường

📝 CHI TIẾT CÁC TRƯỜNG:
   [@company_name] - A2 (header)
   [@report_date] - A3 (header)
   ...
============================================================

📄 Báo cáo đã được lưu: field_check_report.json
✅ Hoàn thành kiểm tra!
```

### File JSON Report
```json
{
  "filePath": "examples/sample_excel.xlsx",
  "timestamp": "2025-07-23T03:30:45.123Z",
  "configuration": [
    {
      "section": "header",
      "startRow": 1,
      "endRow": 10
    }
  ],
  "totalFields": 15,
  "fieldsBySection": [
    {
      "section": "header",
      "count": 10,
      "fields": [...]
    }
  ],
  "allFields": [
    {
      "fieldName": "company_name",
      "cellAddress": "A2",
      "rowNumber": 2,
      "colNumber": 1,
      "section": "header",
      "fullText": "Company: [@company_name]",
      "matchedPattern": "[@company_name]"
    }
  ],
  "errors": []
}
```

## 🧪 Testing

Chạy test suite:
```bash
npm test
```

Test bao gồm:
- ✅ Test tạo file Excel mẫu
- ✅ Test đọc và kiểm tra file Excel
- ✅ Test file Excel tùy chỉnh
- ✅ Test các edge cases (file không tồn tại, file trống)
- ✅ Test hiệu suất với file lớn

## 📋 Yêu cầu hệ thống

- Node.js >= 14.0.0
- npm >= 6.0.0

## 📦 Dependencies

- `exceljs`: ^4.4.0 - Thư viện xử lý file Excel

## 🤝 Đóng góp

1. Fork project
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 🔧 Phát triển

### Thêm tính năng mới:
1. Tạo class mới trong thư mục `src/`
2. Implement interface phù hợp
3. Thêm test trong `src/test.js`
4. Cập nhật documentation

### Debug:
```bash
# Chạy với nodemon để auto-reload
npm run dev

# Hoặc debug trực tiếp
node --inspect src/index.js check your_file.xlsx
```

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra file Excel có đúng format không
2. Chạy `npm test` để kiểm tra hệ thống
3. Xem log chi tiết trong console
4. Tạo issue trên GitHub với thông tin chi tiết
