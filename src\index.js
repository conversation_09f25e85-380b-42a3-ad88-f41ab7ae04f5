#!/usr/bin/env node

const ExcelFieldChecker = require('./ExcelFieldChecker');
const ExcelGenerator = require('./ExcelGenerator');
const path = require('path');
const fs = require('fs').promises;

/**
 * Class chính để điều khiển ứng dụng
 */
class Application {
    constructor() {
        this.args = process.argv.slice(2);
        this.commands = {
            'check': this.checkFile.bind(this),
            'generate': this.generateSample.bind(this),
            'help': this.showHelp.bind(this),
            '--help': this.showHelp.bind(this),
            '-h': this.showHelp.bind(this)
        };
    }

    /**
     * Kiểm tra file Excel
     */
    async checkFile() {
        const filePath = this.args[1];
        
        if (!filePath) {
            console.error('❌ Vui lòng cung cấp đường dẫn file Excel');
            console.log('Sử dụng: npm start check <path-to-excel-file>');
            return;
        }

        // Kiểm tra file tồn tại
        try {
            await fs.access(filePath);
        } catch (error) {
            console.error(`❌ File không tồn tại: ${filePath}`);
            return;
        }

        // Thực hiện kiểm tra
        const checker = new ExcelFieldChecker(filePath);
        await checker.run();
    }

    /**
     * Tạo file Excel mẫu
     */
    async generateSample() {
        const outputPath = this.args[1] || 'examples/sample_excel.xlsx';
        
        console.log('🔧 Đang tạo file Excel mẫu...');
        
        const generator = new ExcelGenerator();
        const success = await generator.saveFile(outputPath);
        
        if (success) {
            console.log('\n✅ File mẫu đã được tạo thành công!');
            console.log(`📁 Đường dẫn: ${outputPath}`);
            console.log('\n📋 File chứa:');
            console.log('   - Sheet 1: Dữ liệu với các trường [@field]');
            console.log('   - Sheet 2: Cấu hình sections (header, detail, footer)');
            console.log('\n🚀 Để kiểm tra file này, chạy:');
            console.log(`   npm start check ${outputPath}`);
        }
    }

    /**
     * Hiển thị hướng dẫn sử dụng
     */
    showHelp() {
        console.log(`
📚 EXCEL FIELD CHECKER - Hướng dẫn sử dụng
${'='.repeat(50)}

🎯 Mục đích:
   Kiểm tra các trường [@field] trong file Excel dựa trên cấu hình sections

📋 Cú pháp:
   npm start <command> [options]

🔧 Các lệnh:
   check <file>     Kiểm tra file Excel
   generate [file]  Tạo file Excel mẫu
   help            Hiển thị hướng dẫn này

📝 Ví dụ:
   npm start generate                    # Tạo file mẫu
   npm start generate my_sample.xlsx     # Tạo file mẫu với tên tùy chỉnh
   npm start check sample.xlsx           # Kiểm tra file Excel
   npm start help                        # Hiển thị hướng dẫn

📐 Cấu trúc file Excel:
   - Sheet 1: Dữ liệu chứa các trường [@field_name]
   - Sheet 2: Cấu hình với format:
     | section   | start_row | end_row |
     |-----------|-----------|---------|
     | header    | 1         | 10      |
     | detail    | 11        | 11      |
     | footer    | 12        | 12      |

🎯 Pattern tìm kiếm:
   [@field_name] - Tìm tất cả text có dạng này

📊 Kết quả:
   - Báo cáo chi tiết trên console
   - File JSON: field_check_report.json

${'='.repeat(50)}
        `);
    }

    /**
     * Chạy ứng dụng
     */
    async run() {
        console.log('🚀 Excel Field Checker v1.0.0\n');

        // Nếu không có tham số, hiển thị help
        if (this.args.length === 0) {
            this.showHelp();
            return;
        }

        const command = this.args[0];
        const handler = this.commands[command];

        if (handler) {
            try {
                await handler();
            } catch (error) {
                console.error(`❌ Lỗi thực thi lệnh '${command}': ${error.message}`);
                console.error(error.stack);
            }
        } else {
            console.error(`❌ Lệnh không hợp lệ: ${command}`);
            console.log('Sử dụng "npm start help" để xem hướng dẫn');
        }
    }
}

/**
 * Utility class để xử lý các tác vụ phụ trợ
 */
class Utils {
    /**
     * Kiểm tra và tạo thư mục nếu chưa tồn tại
     */
    static async ensureDirectory(dirPath) {
        try {
            await fs.access(dirPath);
        } catch (error) {
            await fs.mkdir(dirPath, { recursive: true });
            console.log(`📁 Đã tạo thư mục: ${dirPath}`);
        }
    }

    /**
     * Định dạng kích thước file
     */
    static formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * Lấy thông tin file
     */
    static async getFileInfo(filePath) {
        try {
            const stats = await fs.stat(filePath);
            return {
                size: this.formatFileSize(stats.size),
                modified: stats.mtime.toLocaleString('vi-VN'),
                created: stats.birthtime.toLocaleString('vi-VN')
            };
        } catch (error) {
            return null;
        }
    }
}

// Chạy ứng dụng nếu file này được gọi trực tiếp
if (require.main === module) {
    const app = new Application();
    app.run().catch(error => {
        console.error('❌ Lỗi ứng dụng:', error.message);
        process.exit(1);
    });
}

module.exports = { Application, Utils };
