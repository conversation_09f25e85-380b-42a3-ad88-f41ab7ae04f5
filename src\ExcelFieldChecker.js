const ExcelJS = require('exceljs');
const fs = require('fs').promises;

/**
 * Class để kiểm tra các trường [@field] trong file Excel
 * dựa trên cấu hình từ sheet 2
 */
class ExcelFieldChecker {
    constructor(filePath) {
        this.filePath = filePath;
        this.workbook = new ExcelJS.Workbook();
        this.config = new Map();
        this.foundFields = [];
        this.errors = [];
    }

    /**
     * Khởi tạo và đọc file Excel
     */
    async initialize() {
        try {
            await this.workbook.xlsx.readFile(this.filePath);
            console.log(`✅ Đã đọc file Excel: ${this.filePath}`);
            return true;
        } catch (error) {
            this.errors.push(`Lỗi đọc file Excel: ${error.message}`);
            console.error(`❌ ${this.errors[this.errors.length - 1]}`);
            return false;
        }
    }

    /**
     * <PERSON><PERSON><PERSON> c<PERSON><PERSON> hình từ sheet 2
     * Format: section | start_row | end_row
     */
    async loadConfiguration() {
        try {
            const configSheet = this.workbook.getWorksheet(2);
            if (!configSheet) {
                throw new Error('Không tìm thấy sheet 2 (config sheet)');
            }

            console.log('📋 Đang đọc cấu hình từ sheet 2...');
            
            // Đọc từ row 2 (bỏ qua header)
            configSheet.eachRow((row, rowNumber) => {
                if (rowNumber === 1) return; // Skip header
                
                const section = row.getCell(1).value;
                const startRow = row.getCell(2).value;
                const endRow = row.getCell(3).value;

                if (section && startRow && endRow) {
                    this.config.set(section.toString().toLowerCase(), {
                        section: section.toString(),
                        startRow: parseInt(startRow),
                        endRow: parseInt(endRow)
                    });
                    console.log(`   📌 ${section}: rows ${startRow}-${endRow}`);
                }
            });

            if (this.config.size === 0) {
                throw new Error('Không tìm thấy cấu hình hợp lệ trong sheet 2');
            }

            return true;
        } catch (error) {
            this.errors.push(`Lỗi đọc cấu hình: ${error.message}`);
            console.error(`❌ ${this.errors[this.errors.length - 1]}`);
            return false;
        }
    }

    /**
     * Tìm kiếm các trường [@field] trong sheet 1
     */
    async findFields() {
        try {
            const dataSheet = this.workbook.getWorksheet(1);
            if (!dataSheet) {
                throw new Error('Không tìm thấy sheet 1 (data sheet)');
            }

            console.log('🔍 Đang tìm kiếm các trường [@field]...');
            this.foundFields = [];

            // Regex để tìm pattern [@field]
            const fieldPattern = /@\[([^\]]+)\]/g;

            dataSheet.eachRow((row, rowNumber) => {
                row.eachCell((cell, colNumber) => {
                    const cellValue = cell.value;
                    if (cellValue && typeof cellValue === 'string') {
                        let match;
                        while ((match = fieldPattern.exec(cellValue)) !== null) {
                            const fieldName = match[1];
                            const section = this.determineSectionByRow(rowNumber);
                            
                            this.foundFields.push({
                                fieldName: fieldName,
                                cellAddress: cell.address,
                                rowNumber: rowNumber,
                                colNumber: colNumber,
                                section: section,
                                fullText: cellValue,
                                matchedPattern: match[0]
                            });

                            console.log(`   🎯 Tìm thấy: ${match[0]} tại ${cell.address} (${section})`);
                        }
                    }
                });
            });

            console.log(`✅ Tổng cộng tìm thấy ${this.foundFields.length} trường [@field]`);
            return this.foundFields;
        } catch (error) {
            this.errors.push(`Lỗi tìm kiếm trường: ${error.message}`);
            console.error(`❌ ${this.errors[this.errors.length - 1]}`);
            return [];
        }
    }

    /**
     * Xác định section dựa trên số row
     */
    determineSectionByRow(rowNumber) {
        for (const [sectionName, config] of this.config) {
            if (rowNumber >= config.startRow && rowNumber <= config.endRow) {
                return config.section;
            }
        }
        return 'unknown';
    }

    /**
     * Lấy thống kê theo section
     */
    getStatisticsBySection() {
        const stats = new Map();
        
        for (const field of this.foundFields) {
            const section = field.section;
            if (!stats.has(section)) {
                stats.set(section, {
                    section: section,
                    count: 0,
                    fields: []
                });
            }
            
            const sectionStats = stats.get(section);
            sectionStats.count++;
            sectionStats.fields.push(field);
        }

        return Array.from(stats.values());
    }

    /**
     * Xuất báo cáo chi tiết
     */
    generateReport() {
        const report = {
            filePath: this.filePath,
            timestamp: new Date().toISOString(),
            configuration: Array.from(this.config.values()),
            totalFields: this.foundFields.length,
            fieldsBySection: this.getStatisticsBySection(),
            allFields: this.foundFields,
            errors: this.errors
        };

        return report;
    }

    /**
     * Lưu báo cáo ra file JSON
     */
    async saveReport(outputPath = 'field_check_report.json') {
        try {
            const report = this.generateReport();
            await fs.writeFile(outputPath, JSON.stringify(report, null, 2), 'utf8');
            console.log(`📄 Báo cáo đã được lưu: ${outputPath}`);
            return true;
        } catch (error) {
            console.error(`❌ Lỗi lưu báo cáo: ${error.message}`);
            return false;
        }
    }

    /**
     * In báo cáo ra console
     */
    printReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 BÁO CÁO KIỂM TRA TRƯỜNG [@FIELD]');
        console.log('='.repeat(60));
        
        console.log(`📁 File: ${this.filePath}`);
        console.log(`⏰ Thời gian: ${new Date().toLocaleString('vi-VN')}`);
        console.log(`📋 Tổng số trường tìm thấy: ${this.foundFields.length}`);
        
        console.log('\n📐 CẤU HÌNH SECTIONS:');
        for (const config of this.config.values()) {
            console.log(`   ${config.section}: rows ${config.startRow}-${config.endRow}`);
        }

        console.log('\n📈 THỐNG KÊ THEO SECTION:');
        const stats = this.getStatisticsBySection();
        for (const stat of stats) {
            console.log(`   ${stat.section}: ${stat.count} trường`);
        }

        console.log('\n📝 CHI TIẾT CÁC TRƯỜNG:');
        for (const field of this.foundFields) {
            console.log(`   ${field.matchedPattern} - ${field.cellAddress} (${field.section})`);
        }

        if (this.errors.length > 0) {
            console.log('\n❌ LỖI:');
            for (const error of this.errors) {
                console.log(`   ${error}`);
            }
        }

        console.log('\n' + '='.repeat(60));
    }

    /**
     * Thực hiện toàn bộ quy trình kiểm tra
     */
    async run() {
        console.log('🚀 Bắt đầu kiểm tra trường [@field] trong Excel...\n');
        
        // Khởi tạo
        if (!(await this.initialize())) {
            return false;
        }

        // Đọc cấu hình
        if (!(await this.loadConfiguration())) {
            return false;
        }

        // Tìm kiếm trường
        await this.findFields();

        // In báo cáo
        this.printReport();

        // Lưu báo cáo
        await this.saveReport();

        console.log('\n✅ Hoàn thành kiểm tra!');
        return true;
    }
}

module.exports = ExcelFieldChecker;
