const ExcelFieldChecker = require('./ExcelFieldChecker');
const ExcelGenerator = require('./ExcelGenerator');
const fs = require('fs').promises;
const path = require('path');

/**
 * Class để test các chức năng của ứng dụng
 */
class TestRunner {
    constructor() {
        this.testResults = [];
        this.testFiles = [];
    }

    /**
     * Ghi log kết quả test
     */
    log(testName, success, message = '') {
        const result = {
            test: testName,
            success: success,
            message: message,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    /**
     * Test tạo file Excel mẫu
     */
    async testGenerateExcel() {
        try {
            const generator = new ExcelGenerator();
            const testFile = 'test_sample.xlsx';
            
            const success = await generator.saveFile(testFile);
            this.testFiles.push(testFile);
            
            if (success) {
                // Kiểm tra file có tồn tại không
                await fs.access(testFile);
                this.log('Generate Excel', true, 'File Excel mẫu được tạo thành công');
            } else {
                this.log('Generate Excel', false, 'Không thể tạo file Excel');
            }
        } catch (error) {
            this.log('Generate Excel', false, `Lỗi: ${error.message}`);
        }
    }

    /**
     * Test đọc và kiểm tra file Excel
     */
    async testCheckExcel() {
        try {
            const testFile = 'test_sample.xlsx';
            
            // Kiểm tra file tồn tại
            await fs.access(testFile);
            
            const checker = new ExcelFieldChecker(testFile);
            
            // Test initialize
            const initSuccess = await checker.initialize();
            if (!initSuccess) {
                this.log('Check Excel - Initialize', false, 'Không thể khởi tạo checker');
                return;
            }
            this.log('Check Excel - Initialize', true, 'Khởi tạo thành công');

            // Test load configuration
            const configSuccess = await checker.loadConfiguration();
            if (!configSuccess) {
                this.log('Check Excel - Load Config', false, 'Không thể đọc cấu hình');
                return;
            }
            this.log('Check Excel - Load Config', true, `Đọc được ${checker.config.size} sections`);

            // Test find fields
            const fields = await checker.findFields();
            this.log('Check Excel - Find Fields', true, `Tìm thấy ${fields.length} trường [@field]`);

            // Test generate report
            const report = checker.generateReport();
            if (report && report.totalFields > 0) {
                this.log('Check Excel - Generate Report', true, `Báo cáo có ${report.totalFields} trường`);
            } else {
                this.log('Check Excel - Generate Report', false, 'Báo cáo trống hoặc lỗi');
            }

        } catch (error) {
            this.log('Check Excel', false, `Lỗi: ${error.message}`);
        }
    }

    /**
     * Test tạo file Excel tùy chỉnh
     */
    async testCustomExcel() {
        try {
            const generator = new ExcelGenerator();
            const customFile = 'test_custom.xlsx';
            
            const customData = {
                configs: [
                    { section: 'header', start_row: 1, end_row: 3 },
                    { section: 'body', start_row: 4, end_row: 8 },
                    { section: 'footer', start_row: 9, end_row: 10 }
                ],
                fields: [
                    '[@title]', '[@author]', '[@date]',
                    '[@content1]', '[@content2]', '[@content3]',
                    '[@summary]', '[@signature]'
                ]
            };

            const success = await generator.createCustomFile(customFile, customData);
            this.testFiles.push(customFile);
            
            if (success) {
                // Test kiểm tra file custom
                const checker = new ExcelFieldChecker(customFile);
                await checker.initialize();
                await checker.loadConfiguration();
                const fields = await checker.findFields();
                
                this.log('Custom Excel', true, `File tùy chỉnh có ${fields.length} trường`);
            } else {
                this.log('Custom Excel', false, 'Không thể tạo file tùy chỉnh');
            }
        } catch (error) {
            this.log('Custom Excel', false, `Lỗi: ${error.message}`);
        }
    }

    /**
     * Test các edge cases
     */
    async testEdgeCases() {
        try {
            // Test file không tồn tại
            const checker = new ExcelFieldChecker('nonexistent.xlsx');
            const initResult = await checker.initialize();
            
            if (!initResult && checker.errors.length > 0) {
                this.log('Edge Case - File Not Found', true, 'Xử lý file không tồn tại đúng');
            } else {
                this.log('Edge Case - File Not Found', false, 'Không xử lý được file không tồn tại');
            }

            // Test file Excel trống
            const emptyGenerator = new ExcelGenerator();
            const emptyFile = 'test_empty.xlsx';
            
            // Tạo file Excel chỉ có header, không có [@field]
            emptyGenerator.workbook = new (require('exceljs')).Workbook();
            const ws1 = emptyGenerator.workbook.addWorksheet('Data');
            const ws2 = emptyGenerator.workbook.addWorksheet('Config');
            
            ws1.getCell('A1').value = 'No fields here';
            ws2.getCell('A1').value = 'section';
            ws2.getCell('B1').value = 'start_row';
            ws2.getCell('C1').value = 'end_row';
            ws2.getCell('A2').value = 'test';
            ws2.getCell('B2').value = 1;
            ws2.getCell('C2').value = 5;
            
            await emptyGenerator.workbook.xlsx.writeFile(emptyFile);
            this.testFiles.push(emptyFile);
            
            const emptyChecker = new ExcelFieldChecker(emptyFile);
            await emptyChecker.initialize();
            await emptyChecker.loadConfiguration();
            const emptyFields = await emptyChecker.findFields();
            
            if (emptyFields.length === 0) {
                this.log('Edge Case - No Fields', true, 'Xử lý file không có [@field] đúng');
            } else {
                this.log('Edge Case - No Fields', false, 'Tìm thấy field khi không nên có');
            }

        } catch (error) {
            this.log('Edge Cases', false, `Lỗi: ${error.message}`);
        }
    }

    /**
     * Test performance với file lớn
     */
    async testPerformance() {
        try {
            const generator = new ExcelGenerator();
            const largeFile = 'test_large.xlsx';
            
            // Tạo file với nhiều dữ liệu
            generator.workbook = new (require('exceljs')).Workbook();
            const ws1 = generator.workbook.addWorksheet('Data');
            const ws2 = generator.workbook.addWorksheet('Config');
            
            // Config
            ws2.getCell('A1').value = 'section';
            ws2.getCell('B1').value = 'start_row';
            ws2.getCell('C1').value = 'end_row';
            ws2.getCell('A2').value = 'large_section';
            ws2.getCell('B2').value = 1;
            ws2.getCell('C2').value = 1000;
            
            // Tạo 1000 rows với [@field]
            for (let i = 1; i <= 1000; i++) {
                ws1.getCell(`A${i}`).value = `Row ${i} with [@field_${i}]`;
                ws1.getCell(`B${i}`).value = `Another [@test_field_${i}] here`;
                if (i % 100 === 0) {
                    ws1.getCell(`C${i}`).value = `Special [@special_${i}] field`;
                }
            }
            
            await generator.workbook.xlsx.writeFile(largeFile);
            this.testFiles.push(largeFile);
            
            // Test thời gian xử lý
            const startTime = Date.now();
            const checker = new ExcelFieldChecker(largeFile);
            await checker.initialize();
            await checker.loadConfiguration();
            const fields = await checker.findFields();
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            this.log('Performance Test', true, 
                `Xử lý ${fields.length} trường trong ${processingTime}ms`);
            
        } catch (error) {
            this.log('Performance Test', false, `Lỗi: ${error.message}`);
        }
    }

    /**
     * Dọn dẹp file test
     */
    async cleanup() {
        console.log('\n🧹 Dọn dẹp file test...');
        for (const file of this.testFiles) {
            try {
                await fs.unlink(file);
                console.log(`   🗑️  Đã xóa: ${file}`);
            } catch (error) {
                console.log(`   ⚠️  Không thể xóa: ${file}`);
            }
        }
        
        // Xóa file báo cáo test
        try {
            await fs.unlink('field_check_report.json');
            console.log('   🗑️  Đã xóa: field_check_report.json');
        } catch (error) {
            // Ignore if file doesn't exist
        }
    }

    /**
     * Tạo báo cáo test
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 BÁO CÁO TEST');
        console.log('='.repeat(60));
        console.log(`📋 Tổng số test: ${totalTests}`);
        console.log(`✅ Thành công: ${passedTests}`);
        console.log(`❌ Thất bại: ${failedTests}`);
        console.log(`📈 Tỷ lệ thành công: ${((passedTests/totalTests)*100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ CÁC TEST THẤT BẠI:');
            this.testResults
                .filter(r => !r.success)
                .forEach(r => console.log(`   ${r.test}: ${r.message}`));
        }
        
        console.log('\n' + '='.repeat(60));
        
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests/totalTests)*100
        };
    }

    /**
     * Chạy tất cả test
     */
    async runAllTests() {
        console.log('🧪 Bắt đầu chạy test suite...\n');
        
        await this.testGenerateExcel();
        await this.testCheckExcel();
        await this.testCustomExcel();
        await this.testEdgeCases();
        await this.testPerformance();
        
        const report = this.generateTestReport();
        
        await this.cleanup();
        
        console.log('\n✅ Hoàn thành test suite!');
        
        return report.failed === 0;
    }
}

// Chạy test nếu file này được gọi trực tiếp
if (require.main === module) {
    const testRunner = new TestRunner();
    testRunner.runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Lỗi test suite:', error.message);
            process.exit(1);
        });
}

module.exports = TestRunner;
