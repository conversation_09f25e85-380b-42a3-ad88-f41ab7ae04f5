const ExcelJS = require('exceljs');

/**
 * Class để tạo file Excel mẫu cho việc test
 */
class ExcelGenerator {
    constructor() {
        this.workbook = new ExcelJS.Workbook();
    }

    /**
     * Tạo sheet 1 với dữ liệu mẫu chứa các trường [@field]
     */
    createDataSheet() {
        const worksheet = this.workbook.addWorksheet('Data Sheet');

        // Thiết lập độ rộng cột
        worksheet.columns = [
            { header: 'A', key: 'col_a', width: 20 },
            { header: 'B', key: 'col_b', width: 25 },
            { header: 'C', key: 'col_c', width: 30 },
            { header: 'D', key: 'col_d', width: 15 }
        ];

        // Header section (rows 1-10)
        worksheet.getCell('A1').value = 'HEADER SECTION';
        worksheet.getCell('A2').value = 'Company: [@company_name]';
        worksheet.getCell('A3').value = 'Report Date: [@report_date]';
        worksheet.getCell('A4').value = 'Prepared by: [@prepared_by]';
        worksheet.getCell('B2').value = 'Address: [@company_address]';
        worksheet.getCell('B3').value = 'Phone: [@phone_number]';
        worksheet.getCell('C2').value = 'Email: [@email]';
        worksheet.getCell('C3').value = 'Website: [@website]';
        worksheet.getCell('A5').value = 'Report Title: [@report_title]';
        worksheet.getCell('A6').value = 'Period: [@period_from] to [@period_to]';

        // Detail section (row 11)
        worksheet.getCell('A11').value = 'DETAIL SECTION';
        worksheet.getCell('B11').value = 'Customer: [@customer_name]';
        worksheet.getCell('C11').value = 'Amount: [@amount]';
        worksheet.getCell('D11').value = 'Status: [@status]';

        // Footer section (row 12)
        worksheet.getCell('A12').value = 'FOOTER SECTION';
        worksheet.getCell('B12').value = 'Total: [@total_amount]';
        worksheet.getCell('C12').value = 'Signature: [@signature]';
        worksheet.getCell('D12').value = 'Date: [@signature_date]';

        // Thêm một số dữ liệu bổ sung
        worksheet.getCell('A15').value = 'Additional field: [@additional_field]';
        worksheet.getCell('B16').value = 'Note: This contains [@note_field] for testing';

        // Định dạng header
        worksheet.getRow(1).font = { bold: true, size: 14 };
        worksheet.getRow(11).font = { bold: true, size: 12 };
        worksheet.getRow(12).font = { bold: true, size: 12 };

        // Màu nền cho các section
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6F3FF' }
        };
        worksheet.getRow(11).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFEEE6' }
        };
        worksheet.getRow(12).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6FFE6' }
        };

        console.log('✅ Đã tạo Data Sheet với các trường [@field] mẫu');
        return worksheet;
    }

    /**
     * Tạo sheet 2 với cấu hình sections
     */
    createConfigSheet() {
        const worksheet = this.workbook.addWorksheet('Config Sheet');

        // Thiết lập header
        worksheet.columns = [
            { header: 'section', key: 'section', width: 15 },
            { header: 'start_row', key: 'start_row', width: 12 },
            { header: 'end_row', key: 'end_row', width: 12 }
        ];

        // Thêm dữ liệu cấu hình
        const configData = [
            { section: 'header', start_row: 1, end_row: 10 },
            { section: 'detail', start_row: 11, end_row: 11 },
            { section: 'footer', start_row: 12, end_row: 12 }
        ];

        configData.forEach((config, index) => {
            const row = worksheet.getRow(index + 2); // Bắt đầu từ row 2
            row.values = [config.section, config.start_row, config.end_row];
        });

        // Định dạng header
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFCCCCCC' }
        };

        // Thêm border cho bảng
        const range = worksheet.getCell('A1:C4');
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber <= 4) {
                row.eachCell((cell, colNumber) => {
                    if (colNumber <= 3) {
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                    }
                });
            }
        });

        console.log('✅ Đã tạo Config Sheet với cấu hình sections');
        return worksheet;
    }

    /**
     * Lưu file Excel
     */
    async saveFile(filePath = 'examples/sample_excel.xlsx') {
        try {
            // Tạo các sheet
            this.createDataSheet();
            this.createConfigSheet();

            // Lưu file
            await this.workbook.xlsx.writeFile(filePath);
            console.log(`📁 File Excel mẫu đã được tạo: ${filePath}`);
            return true;
        } catch (error) {
            console.error(`❌ Lỗi tạo file Excel: ${error.message}`);
            return false;
        }
    }

    /**
     * Tạo file Excel với dữ liệu tùy chỉnh
     */
    async createCustomFile(filePath, customData = {}) {
        try {
            this.workbook = new ExcelJS.Workbook(); // Reset workbook
            
            const dataSheet = this.workbook.addWorksheet('Data Sheet');
            const configSheet = this.workbook.addWorksheet('Config Sheet');

            // Tạo config sheet trước
            configSheet.columns = [
                { header: 'section', key: 'section', width: 15 },
                { header: 'start_row', key: 'start_row', width: 12 },
                { header: 'end_row', key: 'end_row', width: 12 }
            ];

            const configs = customData.configs || [
                { section: 'header', start_row: 1, end_row: 5 },
                { section: 'detail', start_row: 6, end_row: 10 },
                { section: 'footer', start_row: 11, end_row: 15 }
            ];

            configs.forEach((config, index) => {
                const row = configSheet.getRow(index + 2);
                row.values = [config.section, config.start_row, config.end_row];
            });

            // Tạo data sheet với dữ liệu tùy chỉnh
            dataSheet.columns = [
                { header: 'A', key: 'col_a', width: 25 },
                { header: 'B', key: 'col_b', width: 25 },
                { header: 'C', key: 'col_c', width: 25 }
            ];

            const sampleFields = customData.fields || [
                '[@field1]', '[@field2]', '[@field3]',
                '[@name]', '[@date]', '[@amount]',
                '[@total]', '[@signature]', '[@note]'
            ];

            // Phân bố các field vào các section
            let currentRow = 1;
            for (const config of configs) {
                for (let row = config.start_row; row <= config.end_row; row++) {
                    if (sampleFields.length > 0) {
                        const field = sampleFields.shift();
                        dataSheet.getCell(`A${row}`).value = `${config.section.toUpperCase()}: ${field}`;
                        dataSheet.getCell(`B${row}`).value = `Sample text with ${field} embedded`;
                        dataSheet.getCell(`C${row}`).value = `Another ${field} example`;
                    }
                }
            }

            await this.workbook.xlsx.writeFile(filePath);
            console.log(`📁 File Excel tùy chỉnh đã được tạo: ${filePath}`);
            return true;
        } catch (error) {
            console.error(`❌ Lỗi tạo file Excel tùy chỉnh: ${error.message}`);
            return false;
        }
    }
}

module.exports = ExcelGenerator;
